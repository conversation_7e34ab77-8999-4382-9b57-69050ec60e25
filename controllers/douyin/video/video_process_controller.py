"""
TrendInsight 视频处理控制器

负责处理视频ID、获取视频详情和处理视频相关数据的业务逻辑
"""

import asyncio
import logging
from typing import Dict, List, Optional

from fastapi import HTTPException

from models.douyin.models import DouyinAweme, update_douyin_aweme
from mappers.douyin.rpc_mapper import RPCDataMapper
from rpc.douyin import async_douyin_api
from rpc.douyin.schemas import VideoDetailRequest, VideoInfo
from schemas.douyin import AwemeDataItem
from rpc.trendinsight.config import TrendInsightConfig


class VideoProcessController:
    """TrendInsight 视频处理控制器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = TrendInsightConfig()

    async def process_video_id(self, aweme_id: str) -> Dict:
        """
        处理视频ID并提取相关数据

        借鉴 controllers/douyin.py 中的 process_video_id 方法实现逻辑：
        1. 先查数据库，存在则直接返回
        2. 不存在则通过RPC获取并存储
        3. 统一的错误处理和返回格式

        Args:
            aweme_id: 抖音视频ID

        Returns:
            Dict: 处理后的视频数据，格式与douyin控制器保持一致
        """
        try:
            # 验证视频ID格式（应该是数字）
            if not aweme_id.isdigit():
                raise HTTPException(status_code=400, detail="无效的视频ID格式")

            # 先尝试从数据库获取
            db_video = await DouyinAweme.filter(aweme_id=aweme_id).first()
            if db_video:
                return {
                    "video_id": aweme_id,
                    "input_type": "aweme_id",
                    "original_input": aweme_id,
                    "processed": True,
                    "data": await db_video.to_dict(),
                    "source": "database",
                }

            # 数据库中不存在，通过RPC获取
            self.logger.info(f"数据库中不存在视频 {aweme_id}，通过RPC获取")

            # 通过 douyin RPC API 获取视频详情（cookies 由账号提供者自动管理）
            request = VideoDetailRequest(aweme_id=aweme_id)
            video_detail = await async_douyin_api.get_video_detail(request)

            # 转换为数据库模型格式
            processed_data = self._convert_video_info_to_aweme_data(video_detail.aweme_detail)

            # 将 AwemeDataItem 转换为 DouyinAweme 实例
            aweme_dict = processed_data.model_dump()
            aweme_instance = DouyinAweme(**aweme_dict)

            # 保存到数据库
            success = await update_douyin_aweme(aweme_instance)

            if success:
                # 重新从数据库获取完整数据
                db_video = await DouyinAweme.filter(aweme_id=aweme_id).first()
                if db_video:
                    return {
                        "video_id": aweme_id,
                        "input_type": "aweme_id",
                        "original_input": aweme_id,
                        "processed": True,
                        "data": await db_video.to_dict(),
                        "source": "rpc_api",
                    }

            # 即使保存失败，也返回RPC数据
            return {
                "video_id": aweme_id,
                "input_type": "aweme_id",
                "original_input": aweme_id,
                "processed": True,
                "data": processed_data,
                "source": "rpc_api",
            }

        except HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"处理视频ID失败: {aweme_id}, 错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"处理视频ID失败: {str(e)}")

    async def fetch_missing_video_details(self, aweme_ids: List[str], keyword_id: Optional[int] = None) -> None:
        """
        后台任务：串行获取缺失的视频详情数据

        功能：
        1. 检查哪些视频ID在 douyin_aweme 表中不存在
        2. 串行调用抖音RPC服务获取缺失视频的详细数据，每个视频间隔2秒
        3. 将获取到的视频数据存储到 douyin_aweme 表中
        4. 对单个视频获取失败进行容错处理，不影响其他视频的获取
        5. 记录后台任务的执行状态和结果

        Args:
            aweme_ids: 需要检查和获取的视频ID列表
            keyword_id: 可选的关键词ID，用于关联视频来源
        """
        if not aweme_ids:
            self.logger.info("没有视频ID需要处理，跳过后台任务")
            return

        keyword_info = f" (关键词ID: {keyword_id})" if keyword_id else ""
        self.logger.info(f"开始后台任务：检查和获取 {len(aweme_ids)} 个视频的详情数据{keyword_info}")

        try:
            # 1. 检查哪些视频在数据库中不存在
            existing_aweme_ids = await DouyinAweme.filter(aweme_id__in=aweme_ids).values_list("aweme_id", flat=True)

            missing_aweme_ids = [aweme_id for aweme_id in aweme_ids if aweme_id not in existing_aweme_ids]

            if not missing_aweme_ids:
                self.logger.info("所有视频数据已存在，无需获取")
                return

            self.logger.info(f"发现 {len(missing_aweme_ids)} 个缺失的视频，开始串行获取（间隔2秒）")

            # 2. 串行获取视频详情，每个视频间隔2秒
            success_count = 0
            error_count = 0

            for i, aweme_id in enumerate(missing_aweme_ids):
                try:
                    # 获取单个视频详情
                    result = await self._fetch_single_video_detail(aweme_id)

                    if result:
                        success_count += 1
                        self.logger.debug(f"成功获取视频 {aweme_id} ({i+1}/{len(missing_aweme_ids)})")
                    else:
                        error_count += 1
                        self.logger.warning(f"获取视频 {aweme_id} 失败 ({i+1}/{len(missing_aweme_ids)})")

                except Exception as e:
                    error_count += 1
                    self.logger.error(f"获取视频 {aweme_id} 失败: {str(e)} ({i+1}/{len(missing_aweme_ids)})")

                # 如果不是最后一个视频，等待2秒再获取下一个
                if i < len(missing_aweme_ids) - 1:
                    await asyncio.sleep(2)

            self.logger.info(f"后台任务完成：成功获取 {success_count} 个视频，" f"失败 {error_count} 个视频")

        except Exception as e:
            self.logger.error(f"后台任务执行失败: {str(e)}")

    async def fetch_video_trend_scores(self, aweme_ids: List[str]) -> None:
        """
        后台任务：批量获取视频趋势评分并存储到数据库

        功能：
        1. 串行调用 TrendInsight API 获取每个视频的趋势指数数据
        2. 提取 trend 数组中最后一项的值作为趋势评分
        3. 将趋势评分存储到 trendinsight_video 表中
        4. 对单个视频获取失败进行容错处理，不影响其他视频的获取
        5. 记录后台任务的执行状态和结果

        Args:
            aweme_ids: 需要获取趋势数据的视频ID列表
        """
        if not aweme_ids:
            self.logger.info("没有视频ID需要处理趋势数据，跳过后台任务")
            return

        self.logger.info(f"开始后台任务：获取 {len(aweme_ids)} 个视频的趋势数据")

        try:
            from services.trendinsight.video_trend_service import VideoTrendService

            success_count = 0
            error_count = 0

            # 串行获取每个视频的趋势数据，避免API限流
            for i, aweme_id in enumerate(aweme_ids):
                try:
                    self.logger.debug(f"正在获取视频 {aweme_id} 的趋势数据 ({i+1}/{len(aweme_ids)})")

                    # 获取 TrendInsight 控制器实例
                    from controllers.trendinsight.main_controller import trendinsight_main_controller

                    # 调用趋势数据获取函数
                    success = await VideoTrendService.fetch_and_store_video_trend_score(aweme_id, trendinsight_main_controller)

                    if success:
                        success_count += 1
                        self.logger.debug(f"成功获取视频 {aweme_id} 的趋势数据")
                    else:
                        error_count += 1
                        self.logger.warning(f"获取视频 {aweme_id} 的趋势数据失败")

                except Exception as e:
                    error_count += 1
                    self.logger.error(f"获取视频 {aweme_id} 的趋势数据异常: {str(e)}")

                # 如果不是最后一个视频，等待2秒再获取下一个，避免API限流
                if i < len(aweme_ids) - 1:
                    await asyncio.sleep(2)

            self.logger.info(f"趋势数据获取任务完成：成功 {success_count} 个视频，失败 {error_count} 个视频")

        except Exception as e:
            self.logger.error(f"批量获取趋势数据任务执行失败: {str(e)}")

    async def process_keyword_video_index_data(self, video_index_data: List[Dict[str, str]]) -> None:
        """
        后台任务：处理关键词视频的指数数据并存储到数据库

        功能：
        1. 接收关键词搜索返回的视频指数数据
        2. 直接使用返回数据中的 index 字段值
        3. 将指数值存储到 trendinsight_video 表的 keyword_index 字段中
        4. 对单个视频处理失败进行容错处理，不影响其他视频的处理
        5. 记录后台任务的执行状态和结果

        Args:
            video_index_data: 视频指数数据列表，每个元素包含 aweme_id 和 index 字段
                格式: [{"aweme_id": "7123456789", "index": "85.6"}, ...]
        """
        if not video_index_data:
            self.logger.info("没有视频指数数据需要处理，跳过后台任务")
            return

        self.logger.info(f"开始后台任务：处理 {len(video_index_data)} 个视频的指数数据")

        try:
            from models.trendinsight.models import update_trendinsight_video

            success_count = 0
            error_count = 0

            # 串行处理每个视频的指数数据
            for i, video_data in enumerate(video_index_data):
                try:
                    aweme_id = video_data.get("aweme_id")
                    index_value = video_data.get("index")

                    if not aweme_id:
                        self.logger.warning(f"视频数据缺少 aweme_id，跳过处理 ({i+1}/{len(video_index_data)})")
                        error_count += 1
                        continue

                    if not index_value:
                        self.logger.warning(f"视频 {aweme_id} 缺少 index 值，跳过处理 ({i+1}/{len(video_index_data)})")
                        error_count += 1
                        continue

                    self.logger.debug(
                        f"正在处理视频 {aweme_id} 的指数数据: {index_value} ({i+1}/{len(video_index_data)})"
                    )

                    # 转换 index 值为浮点数
                    try:
                        keyword_index = float(index_value)
                    except (ValueError, TypeError):
                        self.logger.warning(f"视频 {aweme_id} 的 index 值转换失败: {index_value}")
                        keyword_index = 0.0

                    # 准备数据并存储到数据库
                    video_update_data = {"aweme_id": aweme_id, "trend_score": keyword_index}

                    success = await update_trendinsight_video(video_update_data)

                    if success:
                        success_count += 1
                        self.logger.debug(f"成功存储视频 {aweme_id} 的指数数据: {keyword_index}")
                    else:
                        error_count += 1
                        self.logger.warning(f"存储视频 {aweme_id} 的指数数据失败")

                except Exception as e:
                    error_count += 1
                    self.logger.error(f"处理视频指数数据异常: {str(e)} ({i+1}/{len(video_index_data)})")

                # 如果不是最后一个视频，等待1秒再处理下一个，避免数据库压力
                if i < len(video_index_data) - 1:
                    await asyncio.sleep(1)

            self.logger.info(f"指数数据处理任务完成：成功 {success_count} 个视频，失败 {error_count} 个视频")

        except Exception as e:
            self.logger.error(f"批量处理指数数据任务执行失败: {str(e)}")

    async def _fetch_single_video_detail(self, aweme_id: str) -> bool:
        """
        获取单个视频的详情数据并存储到数据库

        使用统一的视频处理流程，复用douyin控制器的处理逻辑：
        1. 使用RPC API获取视频详情（主要方案）
        cookies 由账号提供者自动管理

        Args:
            aweme_id: 视频ID

        Returns:
            bool: 是否成功获取并存储
        """
        try:
            # 主要方案：使用RPC API获取视频详情
            self.logger.info(f"尝试RPC API提取, aweme_id: {aweme_id}")
            try:
                # 直接使用 douyin RPC API 获取视频详情
                request = VideoDetailRequest(aweme_id=aweme_id)
                video_response = await async_douyin_api.get_video_detail(request)

                if not video_response.aweme_detail:
                    self.logger.error(f"RPC API 返回空的视频详情: {aweme_id}")
                    return False

                # 转换为数据库模型格式
                video_data = self._convert_video_info_to_aweme_data(video_response.aweme_detail)

                # 将 AwemeDataItem 转换为 DouyinAweme 实例
                aweme_dict = video_data.model_dump()
                aweme_instance = DouyinAweme(**aweme_dict)

                # 存储到数据库
                success = await update_douyin_aweme(aweme_instance)

                if success:
                    self.logger.debug(f"成功通过RPC API存储视频数据: {aweme_id}")
                    return True
                else:
                    self.logger.error(f"RPC API数据存储失败: {aweme_id}")
                    return False
            except Exception as e:
                self.logger.error(f"RPC API提取异常, aweme_id: {aweme_id}, 错误: {str(e)}")
                return False

        except Exception as e:
            self.logger.error(f"获取视频 {aweme_id} 详情失败: {str(e)}")
            return False

    def _convert_video_info_to_aweme_data(self, video_info: VideoInfo) -> AwemeDataItem:
        """
        将抖音RPC返回的VideoInfo转换为DouyinAweme模型所需的数据格式

        Args:
            video_info: 抖音RPC返回的VideoInfo对象

        Returns:
            AwemeDataItem: 转换后的数据对象
        """
        try:
            # 使用新的转换函数
            mapper = RPCDataMapper()
            return mapper.convert_video_info_to_aweme_data(video_info)
        except Exception as e:
            self.logger.error(f"转换视频数据格式失败: {str(e)}")
            # 返回基础数据，确保至少有aweme_id
            aweme_data = {
                "aweme_id": getattr(video_info, "aweme_id", ""),
                "title": getattr(video_info, "desc", ""),
                "desc": getattr(video_info, "desc", ""),
                "create_time": getattr(video_info, "create_time", 0),
            }
            return AwemeDataItem(**aweme_data)


# 创建控制器实例
video_process_controller = VideoProcessController()
