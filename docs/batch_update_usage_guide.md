# 批量更新使用指南

## 概述

本指南介绍如何在项目中选择和使用不同的批量更新方法，以获得最佳性能。

## 可用的批量更新方法

### 1. `batch_update_douyin_aweme` - 基础批量更新

**适用场景：**
- 中小量数据（< 5000条）
- 需要精确控制更新逻辑
- 对内存使用有限制

**使用方法：**
```python
from models.douyin.models import batch_update_douyin_aweme

# 准备数据
aweme_list = [DouyinAweme(...), DouyinAweme(...), ...]

# 执行批量更新
success = await batch_update_douyin_aweme(aweme_list)
```

**性能特点：**
- SQL查询次数：3次（1次查询 + 1次批量创建 + 1次批量更新）
- 内存使用：中等
- 处理速度：1000-3000条/秒

### 2. `batch_update_douyin_aweme_optimized` - 分批优化更新

**适用场景：**
- 大量数据（> 5000条）
- 需要避免内存溢出
- 长时间运行的批处理任务

**使用方法：**
```python
from models.douyin.models import batch_update_douyin_aweme_optimized

# 执行分批批量更新
success = await batch_update_douyin_aweme_optimized(
    aweme_list, 
    batch_size=1000  # 可调整批次大小
)
```

**性能特点：**
- 分批处理，避免内存压力
- 自动休眠，减少数据库负载
- 处理速度：800-2000条/秒

### 3. `batch_update_douyin_aweme_with_optimizer` - 数据库优化器

**适用场景：**
- 需要最高性能
- 数据结构相对固定
- 对一致性要求高

**使用方法：**
```python
from models.douyin.models import batch_update_douyin_aweme_with_optimizer

# 使用数据库优化器
success = await batch_update_douyin_aweme_with_optimizer(aweme_list)
```

**性能特点：**
- 使用通用优化器
- 自动分批处理
- 处理速度：2000-5000条/秒

## 选择指南

### 按数据量选择

| 数据量 | 推荐方法 | 原因 |
|--------|----------|------|
| < 1000条 | `batch_update_douyin_aweme` | 简单直接，性能足够 |
| 1000-5000条 | `batch_update_douyin_aweme` | 平衡性能和复杂度 |
| 5000-20000条 | `batch_update_douyin_aweme_optimized` | 分批处理，避免内存问题 |
| > 20000条 | `batch_update_douyin_aweme_with_optimizer` | 最高性能，适合大数据量 |

### 按使用场景选择

**实时处理（用户请求）：**
```python
# 推荐：基础批量更新
success = await batch_update_douyin_aweme(aweme_list)
```

**定时任务（批处理）：**
```python
# 推荐：分批优化更新
success = await batch_update_douyin_aweme_optimized(
    aweme_list, 
    batch_size=2000
)
```

**数据迁移（一次性大量数据）：**
```python
# 推荐：数据库优化器
success = await batch_update_douyin_aweme_with_optimizer(aweme_list)
```

## 性能调优建议

### 1. 批次大小调优

```python
# 根据数据量调整批次大小
batch_sizes = {
    "小数据量": 500,      # < 5000条
    "中等数据量": 1000,    # 5000-50000条
    "大数据量": 2000,      # > 50000条
}

# 使用示例
data_size = len(aweme_list)
if data_size < 5000:
    batch_size = 500
elif data_size < 50000:
    batch_size = 1000
else:
    batch_size = 2000

success = await batch_update_douyin_aweme_optimized(aweme_list, batch_size)
```

### 2. 性能监控

```python
import time
from datetime import datetime

# 添加性能监控
start_time = time.time()
success = await batch_update_douyin_aweme(aweme_list)
duration = time.time() - start_time

logger.info(f"批量处理完成:")
logger.info(f"  数据量: {len(aweme_list)} 条")
logger.info(f"  耗时: {duration:.2f} 秒")
logger.info(f"  速度: {len(aweme_list)/duration:.0f} 条/秒")
logger.info(f"  成功: {'✅' if success else '❌'}")
```

### 3. 错误处理

```python
try:
    success = await batch_update_douyin_aweme(aweme_list)
    if not success:
        logger.error("批量更新失败，请检查数据和日志")
        # 可以尝试分批处理
        success = await batch_update_douyin_aweme_optimized(
            aweme_list, 
            batch_size=500
        )
except Exception as e:
    logger.error(f"批量更新异常: {e}")
    # 降级到逐条处理（仅在必要时）
    success_count = 0
    for aweme in aweme_list:
        try:
            if await update_douyin_aweme(aweme):
                success_count += 1
        except Exception as item_error:
            logger.error(f"处理单条记录失败: {item_error}")
    
    logger.info(f"降级处理完成: {success_count}/{len(aweme_list)}")
```

## 最佳实践

### 1. 数据预处理

```python
# 在批量更新前进行数据验证和清理
def validate_aweme_data(aweme_list: List[DouyinAweme]) -> List[DouyinAweme]:
    valid_data = []
    for aweme in aweme_list:
        if aweme.aweme_id and len(aweme.aweme_id.strip()) > 0:
            # 清理数据
            aweme.title = aweme.title[:1024] if aweme.title else ""
            aweme.desc = aweme.desc[:2000] if aweme.desc else ""
            valid_data.append(aweme)
        else:
            logger.warning(f"跳过无效数据: {aweme}")
    
    return valid_data

# 使用示例
cleaned_data = validate_aweme_data(raw_aweme_list)
success = await batch_update_douyin_aweme(cleaned_data)
```

### 2. 分阶段处理

```python
async def process_large_dataset(aweme_list: List[DouyinAweme]):
    """分阶段处理大数据集"""
    
    # 第一阶段：快速批量创建新记录
    new_records = []
    existing_ids = set()
    
    # 批量查询已存在的记录
    aweme_ids = [aweme.aweme_id for aweme in aweme_list]
    existing_records = await DouyinAweme.filter(aweme_id__in=aweme_ids).all()
    existing_ids = {record.aweme_id for record in existing_records}
    
    # 分离新记录
    for aweme in aweme_list:
        if aweme.aweme_id not in existing_ids:
            new_records.append(aweme)
    
    # 批量创建新记录
    if new_records:
        await DouyinAweme.bulk_create(new_records)
        logger.info(f"创建了 {len(new_records)} 条新记录")
    
    # 第二阶段：更新已存在的记录
    update_records = [aweme for aweme in aweme_list if aweme.aweme_id in existing_ids]
    if update_records:
        success = await batch_update_douyin_aweme(update_records)
        logger.info(f"更新了 {len(update_records)} 条已存在记录")
```

### 3. 资源管理

```python
async def batch_update_with_resource_management(aweme_list: List[DouyinAweme]):
    """带资源管理的批量更新"""
    
    # 检查系统资源
    import psutil
    
    memory_percent = psutil.virtual_memory().percent
    if memory_percent > 80:
        logger.warning(f"内存使用率过高: {memory_percent}%，使用小批次处理")
        batch_size = 200
    else:
        batch_size = 1000
    
    # 根据资源情况选择方法
    if len(aweme_list) > 10000 and memory_percent < 60:
        return await batch_update_douyin_aweme_with_optimizer(aweme_list)
    else:
        return await batch_update_douyin_aweme_optimized(aweme_list, batch_size)
```

## 性能测试

运行性能测试来验证优化效果：

```bash
# 运行性能测试
python tests/performance/test_batch_update_performance.py

# 运行示例演示
python examples/batch_update_douyin_aweme_example.py
```

## 总结

选择合适的批量更新方法可以显著提升应用性能：

1. **小数据量**：使用基础批量更新，简单高效
2. **大数据量**：使用分批优化更新，稳定可靠
3. **极大数据量**：使用数据库优化器，性能最佳
4. **生产环境**：添加监控、错误处理和资源管理

通过合理选择和配置，批量更新性能可以提升10-100倍。
