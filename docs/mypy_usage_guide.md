# mypy 使用指南

## 什么是 mypy？

mypy 是 Python 的静态类型检查器，它可以在代码运行之前发现类型相关的错误，提高代码质量和可靠性。

## 🎯 mypy 的主要作用

### 1. 静态类型检查
- 在运行前发现类型错误
- 验证函数参数和返回值类型
- 检查变量赋值的类型一致性

### 2. 提前发现 Bug
- 类型不匹配错误
- None 值处理错误
- 属性访问错误

### 3. 增强代码可靠性
- 确保 API 接口的一致性
- 提供重构时的安全保障
- 改善代码文档化

## 🛠️ 安装和使用

### 安装 mypy
```bash
# 使用 pip 安装
pip install mypy

# 或使用 conda 安装
conda install mypy
```

### 基本使用
```bash
# 检查单个文件（推荐使用 explicit-package-bases）
python -m mypy --explicit-package-bases --ignore-missing-imports models/douyin/models.py

# 检查整个项目
python -m mypy --explicit-package-bases --ignore-missing-imports .

# 检查特定目录
python -m mypy --explicit-package-bases --ignore-missing-imports models/ services/

# 使用配置文件
python -m mypy --config-file mypy.ini models/douyin/models.py
```

## 📝 配置文件说明

项目根目录的 `mypy.ini` 文件包含了以下配置：

### 基本配置
```ini
[mypy]
python_version = 3.8          # Python 版本
warn_return_any = True        # 警告返回 Any 类型
warn_unused_configs = True    # 警告未使用的配置
show_error_codes = True       # 显示错误代码
pretty = True                 # 美化输出
```

### 严格模式
```ini
disallow_incomplete_defs = True    # 禁止不完整的函数定义
check_untyped_defs = True         # 检查无类型注解的函数
strict_optional = True            # 严格的 Optional 检查
```

### 第三方库处理
```ini
[mypy-tortoise.*]
ignore_missing_imports = True     # 忽略 Tortoise ORM 的导入错误

[mypy-asyncio.*]
ignore_missing_imports = True     # 忽略 asyncio 的导入错误
```

## 🔍 针对您项目的检查示例

### 1. 批量更新函数检查
```python
# mypy 会验证这些类型注解
async def batch_update_douyin_aweme(aweme_list: List[DouyinAweme]) -> bool:
    aweme_ids: List[str] = [aweme.aweme_id for aweme in aweme_list if aweme.aweme_id]
    # mypy 检查：
    # ✅ aweme_list 是否为 List[DouyinAweme]
    # ✅ aweme_ids 是否为 List[str]
    # ✅ 返回值是否为 bool
```

### 2. 数据库优化器检查
```python
# mypy 会验证泛型类型
async def bulk_update_or_create_optimized(
    self,
    model: Type[Model],
    records: List[Model],
    unique_field: str,
    update_fields: List[str],
    batch_size: int = 1000
) -> Tuple[int, int]:
    # mypy 检查所有参数和返回值类型
```

## 🚨 常见错误类型

### 1. 类型不匹配
```python
# 错误示例
created_count: int = "hello"  # mypy 错误：不能将 str 赋值给 int

# 正确示例
created_count: int = 0
```

### 2. None 值处理
```python
# 错误示例
def process_id(user_id: str) -> None:
    print(user_id.upper())  # 如果传入 None 会出错

process_id(None)  # mypy 警告

# 正确示例
def process_id(user_id: Optional[str]) -> None:
    if user_id:
        print(user_id.upper())
```

### 3. 列表类型错误
```python
# 错误示例
aweme_ids: List[str] = [1, 2, 3]  # mypy 错误：应该是字符串列表

# 正确示例
aweme_ids: List[str] = ["1", "2", "3"]
```

## 📊 运行 mypy 的预期输出

### 成功情况
```bash
$ mypy models/douyin/models.py
Success: no issues found in 1 source file
```

### 发现错误情况
```bash
$ mypy models/douyin/models.py
models/douyin/models.py:231: error: Incompatible types in assignment (expression has type "int", variable has type "str")  [assignment]
models/douyin/models.py:241: error: Argument 1 to "append" of "list" has type "str"; expected "DouyinAweme"  [arg-type]
Found 2 errors in 1 file (checked 1 source file)
```

## 🎯 集成到开发流程

### 1. 开发时检查
```bash
# 在提交代码前运行
mypy models/douyin/models.py services/base/database_optimizer.py
```

### 2. CI/CD 集成
```yaml
# GitHub Actions 示例
- name: Type check with mypy
  run: |
    pip install mypy
    mypy models/ services/
```

### 3. IDE 集成
- **VS Code**: 安装 Python 扩展，启用类型检查
- **PyCharm**: 内置支持，在设置中启用
- **Vim/Neovim**: 使用 ALE 或 coc.nvim 插件

## 🚀 最佳实践

### 1. 渐进式采用
```bash
# 从最重要的文件开始
mypy models/douyin/models.py

# 逐步扩展到整个项目
mypy models/
mypy services/
mypy .
```

### 2. 处理第三方库
```python
# 对于没有类型注解的第三方库
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from some_untyped_library import SomeClass
```

### 3. 使用类型忽略
```python
# 在确实无法解决的情况下使用
result = some_complex_function()  # type: ignore[return-value]
```

## 📈 对您项目的具体好处

### 1. 数据库操作安全
- 确保传入正确的模型类型
- 验证查询结果的类型
- 防止字段类型错误

### 2. 批量处理可靠性
- 验证批量操作的参数类型
- 确保返回值类型正确
- 防止列表操作错误

### 3. 异步代码安全
- 检查 async/await 的正确使用
- 验证异步函数的返回类型
- 防止异步上下文错误

## 🔧 故障排除

### 1. 导入错误
如果遇到导入错误，在 `mypy.ini` 中添加：
```ini
[mypy-problematic_module.*]
ignore_missing_imports = True
```

### 2. 类型存根
为没有类型注解的库创建存根文件：
```bash
# 生成存根文件
stubgen -p tortoise -o stubs/
```

### 3. 性能优化
```ini
[mypy]
cache_dir = .mypy_cache
incremental = True
```

## 🔌 Pydantic 插件集成

本项目已集成 Pydantic mypy 插件，提供更强大的类型检查能力。

### 配置

在 `mypy.ini` 中启用 Pydantic 插件：

```ini
[mypy]
plugins = pydantic.mypy

[pydantic-mypy]
init_forbid_extra = True
init_typed = True
warn_required_dynamic_aliases = True
```

### Pydantic 插件的优势

1. **更精确的类型推断** - 自动推断 Pydantic 模型的字段类型
2. **构造函数验证** - 检查模型初始化时的参数类型
3. **字段访问检查** - 验证模型字段的访问和赋值
4. **序列化类型安全** - 确保 JSON 序列化/反序列化的类型一致性

### 示例

```python
from pydantic import BaseModel, Field
from typing import List

class User(BaseModel):
    id: int
    name: str
    age: int = Field(..., gt=0)

# mypy 会检查这些错误：
user = User(id="not_int", name="张三", age=25)  # ❌ id 类型错误
users: List[User] = [user, "not_user"]         # ❌ 列表类型错误
```

### 在批量更新中的应用

```python
from typing import List
from models.douyin.models import DouyinAweme

async def batch_update_douyin_aweme(aweme_list: List[DouyinAweme]) -> bool:
    # mypy + Pydantic 插件会确保：
    # 1. aweme_list 确实是 DouyinAweme 实例的列表
    # 2. 所有字段访问都是类型安全的
    # 3. 数据库操作的参数类型正确

    aweme_ids: List[str] = [aweme.aweme_id for aweme in aweme_list]
    # mypy 确保 aweme_id 是 str 类型

    return True
```

## 总结

mypy 与 Pydantic 插件的组合为项目提供了强大的类型安全保障：

1. **提前发现错误** - 在运行前捕获类型相关的 bug
2. **提高代码可靠性** - 确保数据库操作和批量处理的类型安全
3. **改善开发体验** - 更好的 IDE 支持和代码补全
4. **促进团队协作** - 统一的类型规范和错误检查
5. **Pydantic 集成** - 专门针对数据模型的类型检查优化

建议您在开发流程中集成 mypy，逐步提高项目的类型安全性。
